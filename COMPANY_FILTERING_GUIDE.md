# Company Filtering Guide

This guide explains how to use the enhanced filtering capabilities for the "Get All Companies" operation in the Kylas n8n node.

## Overview

The Kylas node now supports advanced filtering for companies using complex rule structures. You can specify which fields to return and apply sophisticated filtering conditions.

## Usage Options

### Option 1: Advanced Options (Recommended)

Use the "Advanced Options" section for a better user experience:

1. **Fields to Return**: Specify which fields you want in the response
   - Example: `name,ownedBy,industry,emails,phoneNumbers,businessType`
   - Leave empty to return all fields

2. **Filter Rules (JSON)**: Define complex filtering conditions
   - Supports AND/OR logic
   - Multiple operators available
   - Type-safe field validation

### Option 2: Legacy Rule (JSON String)

For backward compatibility, you can still use the legacy "Rule" field with a complete JSON structure.

## Filter Rule Structure

```json
{
  "condition": "AND",
  "rules": [
    {
      "operator": "equal",
      "id": "country",
      "field": "country", 
      "type": "long",
      "value": 175
    },
    {
      "operator": "contains",
      "id": "name",
      "field": "name",
      "type": "string", 
      "value": "abc"
    }
  ],
  "valid": true
}
```

## Available Operators

- `equal` - Exact match
- `not_equal` - Not equal to
- `contains` - Contains substring (for strings)
- `not_contains` - Does not contain substring
- `greater_than` - Greater than (for numbers/dates)
- `less_than` - Less than (for numbers/dates)
- `between` - Between two values
- `in` - Value is in a list
- `not_in` - Value is not in a list

## Field Types

- `string` - Text fields
- `long` - Integer numbers
- `double` - Decimal numbers  
- `date` - Date fields

## Examples

### Example 1: Filter by Country and Name
```json
{
  "condition": "AND",
  "rules": [
    {
      "operator": "equal",
      "id": "country",
      "field": "country",
      "type": "long", 
      "value": 175
    },
    {
      "operator": "contains",
      "id": "name",
      "field": "name",
      "type": "string",
      "value": "tech"
    }
  ],
  "valid": true
}
```

### Example 2: Multiple Conditions with OR Logic
```json
{
  "condition": "OR",
  "rules": [
    {
      "operator": "equal",
      "id": "industry",
      "field": "industry", 
      "type": "string",
      "value": "Technology"
    },
    {
      "operator": "equal",
      "id": "industry",
      "field": "industry",
      "type": "string", 
      "value": "Healthcare"
    }
  ],
  "valid": true
}
```

### Example 3: Numeric Range Filter
```json
{
  "condition": "AND",
  "rules": [
    {
      "operator": "greater_than",
      "id": "revenue",
      "field": "revenue",
      "type": "double",
      "value": 1000000
    },
    {
      "operator": "less_than", 
      "id": "revenue",
      "field": "revenue",
      "type": "double",
      "value": 10000000
    }
  ],
  "valid": true
}
```

## Complete Request Example

When using Advanced Options:
- **Fields to Return**: `name,ownedBy,industry,emails,phoneNumbers,businessType,createdBy,updatedBy,associatedContacts,associatedDeals,id,customFieldValues`
- **Filter Rules (JSON)**: Use any of the examples above

This will generate a request body like:
```json
{
  "limit": 50,
  "fields": ["name","ownedBy","industry","emails","phoneNumbers","businessType","createdBy","updatedBy","associatedContacts","associatedDeals","id","customFieldValues"],
  "jsonRule": {
    "condition": "AND",
    "rules": [
      {
        "operator": "equal",
        "id": "country", 
        "field": "country",
        "type": "long",
        "value": 175
      },
      {
        "operator": "contains",
        "id": "name",
        "field": "name", 
        "type": "string",
        "value": "abc"
      }
    ],
    "valid": true
  }
}
```

## API Endpoint

The filtered requests are sent to: `POST /v1/search/company?page=0&size={limit}&sort=updatedAt,desc`

## Notes

- The `id` field in each rule can be any unique identifier for that rule
- The `field` should match the actual field name in the Kylas API
- The `type` must match the expected data type for the field
- Set `valid: true` to ensure the rule is processed
- Use `condition: "AND"` for all conditions to be true, `condition: "OR"` for any condition to be true
