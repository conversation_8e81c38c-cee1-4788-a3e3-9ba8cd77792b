import { userDescription } from './resources/user';
import { companyDescription } from './resources/company';
import { leadDescription } from './resources/lead';
import { isValidFieldType } from './resources/lead/customFields';
import {
	INodeType,
	INodeTypeDescription,
	ILoadOptionsFunctions,
	INodePropertyOptions,
	IHookFunctions,
	IExecuteFunctions,
	IHttpRequestMethods,
	IDataObject,
	IHttpRequestOptions,
	NodeApiError,
	JsonObject,
	INodeExecutionData
} from 'n8n-workflow';

export class <PERSON>ylas implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Kyla<PERSON>',
		name: 'kyla<PERSON>',
		icon: { light: 'file:kylas.svg', dark: 'file:kylas.dark.svg' },
		group: ['transform'],
		version: 1,
		subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
		description: 'Interact with the Kylas API',
		defaults: {
			name: '<PERSON><PERSON><PERSON>',
		},
		usableAsTool: true,
		inputs: ['main'],
		outputs: ['main'],
		credentials: [{ name: 'kyla<PERSON><PERSON><PERSON>', required: true }],
		requestDefaults: {
			baseURL: 'https://api-qa.sling-dev.com',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		},
		properties: [
			{
				displayName: 'Resource',
				name: 'resource',
				type: 'options',
				noDataExpression: true,
				options: [
					{
						name: 'User',
						value: 'user',
					},
					{
						name: 'Company',
						value: 'company',
					},
					{
						name: 'Lead',
						value: 'lead',
					},
				],
				default: 'user',
			},
			...userDescription,
			...companyDescription,
			...leadDescription
		],
	};

	methods = {
		loadOptions: {
			async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                console.log("=== getLeadCustomFields CALLED ===");
                const returnData: INodePropertyOptions[] = [];
                const fields = await getCachedSystemFields.call(this);
                (fields as RawFieldData[])
                    .filter(field => field.active
                        && field.standard === false
                        && field.type !== 'LOOK_UP'
                        && field.type !== 'MULTI_PICKLIST'
                        && field.type !== 'PICK_LIST'
                    )
                    .forEach(field => {
                        // Include field type information in the display name and store metadata as JSON
                        const fieldMetadata = {
                            name: field.name,
                            type: field.type,
                            required: field.required || false,
                            picklist: field.picklist
                        };

                        returnData.push({
                            name: `${field.displayName} (${field.type})`,
                            value: JSON.stringify(fieldMetadata),
                            description: `Field type: ${field.type}${field.required ? ' (Required)' : ''}`,
                        });
                    });
                console.log(`getLeadCustomFields returning ${returnData.length} fields`);
                return returnData;
            },

            async getAvailableCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                console.log("=== getAvailableCustomFields CALLED ===");
                const returnData: INodePropertyOptions[] = [];
                const fields = await getCachedSystemFields.call(this);

                // Get already selected custom fields to filter them out
                const selectedFields = new Set<string>();
                try {
                    const customFields = this.getCurrentNodeParameter('customFields.customField') as Array<{fieldName: string}> || [];
                    customFields.forEach(field => {
                        if (field.fieldName) {
                            try {
                                const fieldMetadata = JSON.parse(field.fieldName);
                                selectedFields.add(fieldMetadata.name);
                            } catch (error) {
                                // If parsing fails, use the fieldName directly
                                selectedFields.add(field.fieldName);
                            }
                        }
                    });
                } catch (error) {
                    console.log('Error getting selected fields:', error);
                }

                (fields as RawFieldData[])
                    .filter(field => field.active
                        && field.standard === false
                        && field.type !== 'LOOK_UP'
                        && field.type !== 'MULTI_PICKLIST'
                        && field.type !== 'PICK_LIST'
                    )
                    .forEach(field => {
                        // Only include fields that haven't been selected yet
                        if (!selectedFields.has(field.name)) {
                            const fieldMetadata = {
                                name: field.name,
                                type: field.type,
                                required: field.required || false,
                                picklist: field.picklist
                            };

                            returnData.push({
                                name: `${field.displayName} (${field.type})`,
                                value: JSON.stringify(fieldMetadata),
                                description: `Field type: ${field.type}${field.required ? ' (Required)' : ''}`,
                            });
                        }
                    });

                console.log(`getAvailableCustomFields returning ${returnData.length} available fields (${selectedFields.size} already selected)`);
                return returnData;
            },

            async getPicklistValues(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                console.log("=== getPicklistValues CALLED ===");
                const fieldName = this.getCurrentNodeParameter('fieldName') as string;
                const returnData: INodePropertyOptions[] = [];

                try {
                    // Parse the field metadata to get picklist information
                    const fieldMetadata = JSON.parse(fieldName);
                    if (fieldMetadata.picklist && fieldMetadata.picklist.id) {
                        const picklistId = fieldMetadata.picklist.id;
                        const response = await kylasApiRequest.call(this, 'GET', `/v1/picklists/${picklistId}/values`, {});
                        const picklistData = JSON.parse(response.data);

                        if (picklistData && Array.isArray(picklistData)) {
                            picklistData
                                .filter((item: any) => item.active)
                                .forEach((item: any) => {
                                    returnData.push({
                                        name: item.value,
                                        value: item.value,
                                    });
                                });
                        }
                    }
                } catch (error) {
                    console.log('Error loading picklist values:', error);
                    // Return empty array if there's an error
                }

                console.log(`getPicklistValues returning ${returnData.length} options`);
                return returnData;
            },


		},
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const returnData: INodeExecutionData[] = [];

		for (let i = 0; i < items.length; i++) {
			try {
				const resource = this.getNodeParameter('resource', i) as string;
				const operation = this.getNodeParameter('operation', i) as string;

				let responseData: any;

				if (resource === 'lead') {
					responseData = await handleLeadOperations.call(this, operation, i);
				} else if (resource === 'user') {
					responseData = await handleUserOperations.call(this, operation, i);
				} else if (resource === 'company') {
					responseData = await handleCompanyOperations.call(this, operation, i);
				} else {
					throw new Error(`Unknown resource: ${resource}`);
				}

				if (Array.isArray(responseData)) {
					returnData.push(...responseData.map(item => ({ json: item })));
				} else {
					returnData.push({ json: responseData });
				}
			} catch (error) {
				if (this.continueOnFail()) {
					returnData.push({ json: { error: error.message } });
					continue;
				}
				throw error;
			}
		}

		return [returnData];
	}
}

// Helper functions for handling different operations
async function handleLeadOperations(this: IExecuteFunctions, operation: string, itemIndex: number): Promise<any> {
	switch (operation) {
		case 'create':
			return await createLead.call(this, itemIndex);
		case 'update':
			return await updateLead.call(this, itemIndex);
		case 'get':
			return await getLead.call(this, itemIndex);
		case 'search':
			return await searchLeads.call(this, itemIndex);
		default:
			throw new Error(`Unknown lead operation: ${operation}`);
	}
}

async function handleUserOperations(this: IExecuteFunctions, operation: string, itemIndex: number): Promise<any> {
	switch (operation) {
		case 'create':
			return await createUser.call(this, itemIndex);
		case 'get':
			return await getUser.call(this, itemIndex);
		case 'getAll':
			return await getAllUsers.call(this, itemIndex);
		default:
			throw new Error(`Unknown user operation: ${operation}`);
	}
}

async function handleCompanyOperations(this: IExecuteFunctions, operation: string, itemIndex: number): Promise<any> {
	switch (operation) {
		case 'getAll':
			return await getAllCompanies.call(this, itemIndex);
		default:
			throw new Error(`Unknown company operation: ${operation}`);
	}
}

// Lead operation functions
async function createLead(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const firstName = this.getNodeParameter('firstName', itemIndex) as string;
	const lastName = this.getNodeParameter('lastName', itemIndex) as string;
	const additionalFields = this.getNodeParameter('additionalFields', itemIndex, {}) as IDataObject;
	const customFields = this.getNodeParameter('customFields.customField', itemIndex, []) as Array<{
		fieldName: string;
		value: string;
	}>;

	const body: IDataObject = {
		firstName,
		lastName,
		...additionalFields,
	};

	// Process custom fields with proper type handling
	if (customFields.length > 0) {
		body.customFieldValues = {};
		customFields.forEach(field => {
			if (field.fieldName) {
				try {
					// Parse field metadata to get the actual field name and type
					const fieldMetadata = JSON.parse(field.fieldName);
					const fieldName = fieldMetadata.name;
					const fieldType = fieldMetadata.type;

					// Validate field type
					if (!isValidFieldType(fieldType)) {
						console.warn(`Invalid field type: ${fieldType}, skipping field`);
						return; // Skip this field
					}

					// Convert value based on field type
					let processedValue: any = field.value;

					// Type conversion based on field type
					switch (fieldType) {
						case 'NUMBER':
							console.log(`Processing number field: ${fieldName} = ${field.value}`);
							processedValue = parseFloat(field.value);
							if (isNaN(processedValue)) {
								console.warn(`Invalid number value for ${fieldName}: ${field.value}`);
								return;
							}
							break;
						case 'DATE_PICKER':
						case 'DATETIME_PICKER':
							// Validate date format if needed
							if (field.value && !isNaN(Date.parse(field.value))) {
								processedValue = field.value;
							} else if (field.value) {
								console.warn(`Invalid date value for ${fieldName}: ${field.value}`);
								return;
							}
							break;
						default:
							// For text-based fields, use value as-is
							processedValue = field.value;
					}

					// Only add the field if it has a valid value
					if (processedValue !== undefined && processedValue !== '' && processedValue !== null) {
						(body.customFieldValues as IDataObject)[fieldName] = processedValue;
					}
				} catch (error) {
					// If JSON parsing fails, treat fieldName as the actual field name (backward compatibility)
					console.warn('Failed to parse field metadata, using fieldName directly:', error);
					if (field.value !== undefined && field.value !== '' && field.value !== null) {
						(body.customFieldValues as IDataObject)[field.fieldName] = field.value;
					}
				}
			}
		});
	}
    console.log("createLead body: " + JSON.stringify(body));
	const response = await kylasApiRequest.call(this, 'POST', '/v1/leads', body);
    console.log("createLead response: " + JSON.stringify(response.data));
	return response.data;
}

async function updateLead(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const leadId = this.getNodeParameter('leadId', itemIndex) as string;
	const updateFields = this.getNodeParameter('updateFields', itemIndex, {}) as IDataObject;
	const customFields = this.getNodeParameter('customFields.customField', itemIndex, []) as Array<{
		fieldName: string;
		value: string;
	}>;

	const body: IDataObject = {
		...updateFields,
	};

	// Process custom fields with proper type handling
	if (customFields.length > 0) {
		body.customFieldValues = {};
		customFields.forEach(field => {
			if (field.fieldName) {
				try {
					// Parse field metadata to get the actual field name and type
					const fieldMetadata = JSON.parse(field.fieldName);
					const fieldName = fieldMetadata.name;
					const fieldType = fieldMetadata.type;

					// Validate field type
					if (!isValidFieldType(fieldType)) {
						console.warn(`Invalid field type: ${fieldType}, skipping field`);
						return; // Skip this field
					}

					// Convert value based on field type
					let processedValue: any = field.value;

					// Type conversion based on field type
					switch (fieldType) {
						case 'NUMBER':
							console.log(`Processing number field: ${fieldName} = ${field.value}`);
							processedValue = parseFloat(field.value);
							if (isNaN(processedValue)) {
								console.warn(`Invalid number value for ${fieldName}: ${field.value}`);
								return;
							}
							break;
						case 'DATE_PICKER':
						case 'DATETIME_PICKER':
							// Validate date format if needed
							if (field.value && !isNaN(Date.parse(field.value))) {
								processedValue = field.value;
							} else if (field.value) {
								console.warn(`Invalid date value for ${fieldName}: ${field.value}`);
								return;
							}
							break;
						case 'PICK_LIST':
						case 'MULTI_PICKLIST':
							// For picklist fields, use value as-is
							processedValue = field.value;
							break;
						default:
							// For text-based fields (TEXT_FIELD, URL, etc.), use value as-is
							processedValue = field.value;
					}

					// Only add the field if it has a valid value
					if (processedValue !== undefined && processedValue !== '' && processedValue !== null) {
						(body.customFieldValues as IDataObject)[fieldName] = processedValue;
					}
				} catch (error) {
					// If JSON parsing fails, treat fieldName as the actual field name (backward compatibility)
					console.warn('Failed to parse field metadata, using fieldName directly:', error);
					if (field.value !== undefined && field.value !== '' && field.value !== null) {
						(body.customFieldValues as IDataObject)[field.fieldName] = field.value;
					}
				}
			}
		});
	}

	const response = await kylasApiRequest.call(this, 'PUT', `/v1/leads/${leadId}`, body);
	return JSON.parse(response.data);
}

async function getLead(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const leadId = this.getNodeParameter('leadId', itemIndex) as string;
	const response = await kylasApiRequest.call(this, 'GET', `/v1/leads/${leadId}`, {});
	return JSON.parse(response.data);
}

async function searchLeads(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const additionalOptions = this.getNodeParameter('additionalOptions', itemIndex, {}) as IDataObject;

	// Build query parameters
	const queryParams = new URLSearchParams();
	if (additionalOptions.limit) {
		queryParams.append('limit', additionalOptions.limit.toString());
	}
	if (additionalOptions.offset) {
		queryParams.append('offset', additionalOptions.offset.toString());
	}
	if (additionalOptions.q) {
		queryParams.append('q', additionalOptions.q.toString());
	}

	const queryString = queryParams.toString();
	const endpoint = queryString ? `/v1/leads?${queryString}` : '/v1/leads';

	const response = await kylasApiRequest.call(this, 'GET', endpoint, {});
	return JSON.parse(response.data);
}

// User operation functions
async function createUser(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const name = this.getNodeParameter('name', itemIndex) as string;

	const body: IDataObject = {
		name,
	};

	const response = await kylasApiRequest.call(this, 'POST', '/users', body);
	return JSON.parse(response.data);
}

async function getUser(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const userId = this.getNodeParameter('userId', itemIndex) as string;
	const response = await kylasApiRequest.call(this, 'GET', `/v1/users/${userId}`, {});
	return JSON.parse(response.data);
}

async function getAllUsers(this: IExecuteFunctions, _itemIndex: number): Promise<any> {
	const response = await kylasApiRequest.call(this, 'GET', '/users', {});
	return JSON.parse(response.data);
}

// Company operation functions
async function getAllCompanies(this: IExecuteFunctions, itemIndex: number): Promise<any> {
	const limit = this.getNodeParameter('limit', itemIndex, 50) as number;
	const returnAll = this.getNodeParameter('returnAll', itemIndex, false) as boolean;

	// Build query parameters
	const queryParams = new URLSearchParams();
	if (!returnAll) {
		queryParams.append('limit', limit.toString());
	}

	const queryString = queryParams.toString();
	const endpoint = queryString ? `/companies?${queryString}` : '/companies';

	const response = await kylasApiRequest.call(this, 'GET', endpoint, {});
	return JSON.parse(response.data);
}

// Utility function to get lead custom fields as Fields type
export async function getLeadCustomFieldsAsFields(context: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<Fields> {
    console.log("=== getLeadCustomFieldsAsFields CALLED ===");
    const fieldsData = await getCachedSystemFields.call(context);
    const filteredFields = (fieldsData as RawFieldData[])
        .filter(field => field.active
            && field.standard === false
            && field.type !== 'LOOK_UP'
            && field.type !== 'MULTI_PICKLIST'
            && field.type !== 'PICK_LIST'
        )
        .map(field => ({
            name: field.name,
            displayName: field.displayName,
            type: field.type,
            required: field.required || false,
            standard: field.standard || false,
            internal: field.internal || false
        }));

    const result: Fields = {
        fields: filteredFields
    };

    console.log(`getLeadCustomFieldsAsFields returning ${result.fields.length} fields`);
    return result;
}

let systemFieldsCache: RawFieldData[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<RawFieldData[]> {
    const now = Date.now();

    // Check if cache is valid (not expired)
    if (systemFieldsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('Using cached system fields');
        return systemFieldsCache;
    }

    // Cache is expired or doesn't exist, fetch fresh data
    console.log('Fetching fresh system fields from API');
    const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
    const fields = JSON.parse(customFields.data);

    // Update cache
    systemFieldsCache = fields;
    cacheTimestamp = now;

    return fields;
}


export async function kylasApiRequest(
    this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions,
    method: IHttpRequestMethods,
    endpoint: string,
    body: IDataObject
): Promise<KylasApiResponse> {
    // const authenticationMethod = this.getNodeParameter('authentication', 0);
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options: IHttpRequestOptions = {
        headers: {
            Accept: 'application/json',
        },
        method,
        url: `https://api-qa.sling-dev.com${endpoint}`,
    };

    // console.log("options->" + JSON.stringify(options));

    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }


    try {
        const credentialType = 'kylasApi';
        // console.log("option->" + JSON.stringify(options.body));
        const responseData = await this.helpers.requestWithAuthentication.call(
            this,
            credentialType,
            options,
        );


        // console.log("responseData 2->" + JSON.stringify(responseData));
        if (responseData.success === false) {
            throw new NodeApiError(this.getNode(), responseData as JsonObject);
        }
        console.log("Return success")
        return {
            data: responseData
        };
    } catch (error) {
        throw new NodeApiError(this.getNode(), error as JsonObject);
    }
};

// Interface for API response
interface KylasApiResponse {
    data: string;
    success?: boolean;
}

// Interface for raw field data from API
interface RawFieldData {
    id: number;
    type: string;
    displayName: string;
    name: string;
    active: boolean;
    required: boolean;
    important: boolean;
    standard: boolean;
    width: number;
    column: number;
    row: number;
    multiValue: boolean;
    internal: boolean;
    picklist: unknown;
    systemRequired: boolean;
    inputSchema?: unknown;
}

export type Fields = {
    fields: Array<{
        name: string;
        displayName: string;
        type: string;
        required: boolean;
        standard: boolean;
        internal: boolean;
    }>;
}