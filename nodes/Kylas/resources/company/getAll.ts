import type { INodeProperties } from 'n8n-workflow';

const showOnlyForCompanyGetMany = {
	operation: ['getAll'],
	resource: ['company'],
};

export const companyGetManyDescription: INodeProperties[] = [
	{
		displayName: 'Limit',
		name: 'limit',
		type: 'number',
		displayOptions: {
			show: {
				...showOnlyForCompanyGetMany
			},
		},
		typeOptions: {
			minValue: 1,
			maxValue: 100,
		},
		default: 50,
		description: 'Max number of results to return between 1 to 100',
	},
	{
		displayName: 'Rule',
		name: 'rule',
		type: 'string',
		displayOptions: {
			show: {
				...showOnlyForCompanyGetMany
			},
		},
		default: '',
		description: 'Enter rule to search',
	}
];
