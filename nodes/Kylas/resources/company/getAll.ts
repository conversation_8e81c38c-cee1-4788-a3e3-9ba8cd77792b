import type { INodeProperties } from 'n8n-workflow';

const showOnlyForCompanyGetMany = {
	operation: ['getAll'],
	resource: ['company'],
};

export const companyGetManyDescription: INodeProperties[] = [
	{
		displayName: 'Limit',
		name: 'limit',
		type: 'number',
		displayOptions: {
			show: {
				...showOnlyForCompanyGetMany
			},
		},
		typeOptions: {
			minValue: 1,
			maxValue: 100,
		},
		default: 10,
		description: 'Max number of results to return between 1 to 100',
	},
	{
		displayName: 'Advanced Options',
		name: 'additionalOptions',
		type: 'collection',
		placeholder: 'Add Option',
		displayOptions: {
			show: {
				...showOnlyForCompanyGetMany
			},
		},
		default: {},
		options: [
			{
				displayName: 'Fields to Return',
				name: 'fields',
				type: 'string',
				default: '',
				placeholder: 'name,ownedBy,industry,emails,phoneNumbers',
				description: 'Comma-separated list of fields to return. Leave empty to return all fields.',
			},
			{
				displayName: 'Filter Rules (JSON)',
				name: 'jsonRule',
				type: 'json',
				default: '{\n  "condition": "AND",\n  "rules": [\n    {\n      "operator": "equal",\n      "id": "country",\n      "field": "country",\n      "type": "long",\n      "value": 175\n    },\n    {\n      "operator": "contains",\n      "id": "name",\n      "field": "name",\n      "type": "string",\n      "value": "abc"\n    }\n  ],\n  "valid": true\n}',
				description: 'JSON rule object for filtering companies. Supports complex AND conditions. Available operators: equal, not_equal, contains, not_contains, greater_than, less_than, between, in, not_in. Field types: string, long, double, date.',
			},
		],
	}
];
